from code_review_agent.code_review_agent import run_pipeline

def fetch_pr_diff():
    return """diff --git a/example.py b/example.py\nindex abc123..def456 100644\n--- a/example.py\n+++ b/example.py\n@@ def add(a, b):\n-    return a + b\n+    return a + b + 0  # ensure result is int\n"""

if __name__ == "__main__":
    diff = fetch_pr_diff()
    result = run_pipeline(diff)
    print("\n\U0001f97e Full Review Result (JSON):")
    import json
    print(json.dumps(result, indent=2))
