# AI Code Review Assistant Configuration

# Model Configuration
MODEL_PATH=./models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf
MAX_TOKENS=512
TEMPERATURE=0.1
N_CTX=4096

# Optional: LangSmith tracing (for debugging)
# LANGCHAIN_API_KEY=lsv2_pt_...
# LANGCHAIN_PROJECT=YOUR_PROJECT_NAME
# LANGCHAIN_TRACING_V2=true
# LANGCHAIN_ENDPOINT=https://api.smith.langchain.com

# Optional: OpenAI API (if using OpenAI instead of local model)
# OPENAI_API_KEY=your_openai_api_key_here

# Optional: HuggingFace API (if using HuggingFace models)
# HUGGINGFACE_API_TOKEN=your_huggingface_token_here