import os
from langchain_community.llms import LlamaCpp

_cached_llm = None

def load_local_llm():
    """Load local GGUF model using llama-cpp-python"""
    global _cached_llm
    if _cached_llm is not None:
        return _cached_llm

    # Use relative path from project root
    model_path = "./models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf"

    # Check if model file exists
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model file not found at {model_path}")

    try:
        _cached_llm = LlamaCpp(
            model_path=model_path,
            n_ctx=4096,  # Context window
            n_threads=4,  # Number of CPU threads
            temperature=0.1,  # Low temperature for consistent code review
            max_tokens=512,  # Max tokens to generate
            verbose=False,  # Disable verbose logging
            stop=["</s>", "\n\n"]  # Stop sequences
        )
        return _cached_llm
    except Exception as e:
        raise RuntimeError(f"Failed to load LLM model: {str(e)}")