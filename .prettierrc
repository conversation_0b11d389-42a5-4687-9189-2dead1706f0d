{"printWidth": 88, "tabWidth": 4, "useTabs": false, "semi": true, "singleQuote": false, "quoteProps": "as-needed", "trailingComma": "es5", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "overrides": [{"files": "*.py", "options": {"printWidth": 88, "tabWidth": 4, "parser": "python"}}, {"files": ["*.json", "*.jsonc"], "options": {"tabWidth": 2}}, {"files": ["*.yml", "*.yaml"], "options": {"tabWidth": 2}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}]}