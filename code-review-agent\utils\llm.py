from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
from langchain.llms import HuggingFacePipeline

_cached_llm = None

def load_local_llm():
    global _cached_llm
    if _cached_llm is not None:
        return _cached_llm
    model_id = "../models/DeepSeek-Coder-V2-Lite-Instruct-Q4_K_M.gguf"
    tokenizer = AutoTokenizer.from_pretrained(model_id, trust_remote_code=True)
    model = AutoModelForCausalLM.from_pretrained(model_id, device_map="auto", trust_remote_code=True)
    pipe = pipeline("text-generation", model=model, tokenizer=tokenizer, max_new_tokens=512)
    _cached_llm = HuggingFacePipeline(pipeline=pipe)
    return _cached_llm