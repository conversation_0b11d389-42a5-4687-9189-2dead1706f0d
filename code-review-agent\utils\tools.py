from langchain.tools import tool
import ast
import re
import langdetect
from code-review-agent.utils.llm import load_local_llm

@tool
def scan_structure(input: str) -> list:
    patterns = [
        (r'^\s*(class|record|interface|struct)\s+(\w+)', 2),
        (r'^\s*(def|function)\s+(\w+)', 2),
        (r'\b(?:public|private|protected|internal|static|async|final)?\s*\w+[<>,\s\[\]]*\s+(\w+)\s*\(.*?\)\s*\{?', 1),
        (r'^\s*def\s+(\w+)\s*\(', 1),
        # Variable declarations with types (C#/Java/TS)
        (r'^\s*(?:var|int|float|double|bool|string|char|long|decimal|const|let|final)\s+(\w+)\s*=.*;', 1),
        # TypeScript/JS/Java field/property/assignment
        # (r'^\s*(\w+)\s*=\s*.+;', 1),
    ]
    result = []
    lines = input.splitlines()
    for i, line in enumerate(lines):
        for pattern, group_index in patterns:
            match = re.search(pattern, line)
            if match:
                name = match.group(group_index)
                if name:
                    result.append({"name": name, "line": i + 1})
    return result

@tool
def check_naming(input: str, language: str = "python", declarations: list = None) -> dict:
    if langdetect.detect(input) != "en":
        return {
            "lines": [],
            "summary": "\u26a0\ufe0f Naming check skipped: non-English code"
        }

    llm = load_local_llm()
    if declarations:
        decl_str = "\n".join([f"- {d['name']} (line {d['line']})" for d in declarations])
        extra_info = f"\nDetected declarations:\n{decl_str}\n"
    else:
        extra_info = ""

    decl_str = "\n".join([f"- {d['name']} (line {d['line']})" for d in declarations])
    prompt = f"""
Review the following class, method, and variable names for proper naming conventions.
Check for clarity, spelling, meaningful verbs (e.g., is_*/has_* for booleans), and consistency.
Respond in JSON:
{{
  "lines": [
    {{"line": number, "feedback": "..."}},
    ...
  ],
  "summary": "..."
}}

Names to review:
{decl_str}
"""

    try:
        response = llm.invoke(prompt).strip()
        import json
        return json.loads(response)
    except Exception as e:
        return {
            "lines": [],
            "summary": f"\u274c Failed to parse naming feedback: {str(e)}"
        }