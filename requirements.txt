# Core LangChain packages
langchain==0.1.0
langchain-community==0.0.10
langchain-core==0.1.10
langchain-text-splitters==0.0.1

# LangGraph for workflow orchestration
langgraph==0.0.20

# Local LLM support
llama-cpp-python==0.2.20

# Vector store and embeddings
faiss-cpu==1.7.4
sentence-transformers==2.2.2

# Utilities
langdetect==1.0.9
python-dotenv==1.0.0

# UI framework
streamlit==1.28.0

# Optional: Remove if not using OpenAI
# langchain-openai

# Optional: Remove if not using HuggingFace transformers
# transformers
# huggingface-hub
# accelerate
# torch